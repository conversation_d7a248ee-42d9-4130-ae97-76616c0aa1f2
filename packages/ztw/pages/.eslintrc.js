module.exports = {
  "parser": "@babel/eslint-parser",
  "plugins": ["react","mocha"],
  "parserOptions": {
    "requireConfigFile": false,
    "babelOptions": {
      "presets": ["@babel/preset-react"],      
      "plugins": ["@babel/plugin-transform-class-properties"],      
    },
  },
  "extends": ["airbnb"],
  "globals": {
    "React": true,
    "document": true,
    "window": true,
    "jQuery": true,
    "$": true,
    "localStorage": true,
    "expect": "readonly",
    "fetch": true
  },
  "root": true,
  "rules": {
    "indent": ["error", 2],
    "consistent-return": 0,
    "react/jsx-props-no-spreading": 0,
    "react/prefer-stateless-function": "warn",
    "arrow-body-style": 0,
    "react/self-closing-comp": [
      "warn",
      {
        "component": true,
        "html": false
      }
    ],
    "react/sort-comp": [
      1,
      {
        "order": [
          "static-methods",
          "lifecycle",
          "everything-else",
          "rendering"
        ],
        "groups": {
          "rendering": ["/^render.+$/", "render"]
        }
      }
    ],
    "max-len": "off",
    "class-methods-use-this": "off",
    "default-param-last": "off",
    "no-mixed-operators": "off",
    "no-unused-vars": "off",
    "no-unsafe-optional-chaining": "off",
    "react/prop-types": "off",
    "react/forbid-prop-types": "off",
    "react/state-in-constructor": "off",
    "react/jsx-no-useless-fragment": "off",
    "react/no-unused-class-component-methods": "off",
    "react/no-unstable-nested-components": "off",
    "react/static-property-placement": "off",
    "react/require-default-props": 0,
    "jsx-a11y/href-no-hash": "off",
    "jsx-a11y/anchor-is-valid": ["warn", { "aspects": ["invalidHref"] }],
    "react/jsx-boolean-value": ["warn", "never"],
    "react/jsx-closing-bracket-location": ["warn", "after-props"],
    "react/jsx-curly-spacing": ["warn", "never"],
    "react/jsx-filename-extension": 0,
    "react/jsx-first-prop-new-line": ["warn", "multiline"],
    "import/no-named-as-default": 0,
    "react/jsx-handler-names": [
      "warn",
      {
        "eventHandlerPrefix": "handle",
        "eventHandlerPropPrefix": "on"
      }
    ],
    "react/jsx-indent": ["warn", 2],
    "react/jsx-key": "error",
    "react/jsx-wrap-multilines": ["warn"],
    "react/jsx-indent-props": 0,
    "no-trailing-spaces": [2, { "skipBlankLines": true }],
    "prefer-template": 0,
    "import/prefer-default-export": 0,
    "import/no-unresolved": 0,
    "import/no-extraneous-dependencies": 0,
    "import/extensions": 0,
    "babel/object-curly-spacing": 0,
    "mocha/no-exclusive-tests": "error"
  },
  "env": {
    "es6": true,
    "browser": true,
    "mocha": true,
    "node": true,
    "jest": true,
  },
  "overrides": [
    {
      "files": ["*.test.js"],
      "rules": {
        "no-unused-expressions": "off"
      }
    }
  ]
}
